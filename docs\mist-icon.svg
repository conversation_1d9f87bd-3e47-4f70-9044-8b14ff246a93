<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="mistGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Mist layers -->
  <ellipse cx="16" cy="8" rx="12" ry="3" fill="url(#mistGradient)" opacity="0.4"/>
  <ellipse cx="14" cy="12" rx="10" ry="2.5" fill="url(#mistGradient)" opacity="0.6"/>
  <ellipse cx="18" cy="16" rx="11" ry="3" fill="url(#mistGradient)" opacity="0.5"/>
  <ellipse cx="15" cy="20" rx="9" ry="2.5" fill="url(#mistGradient)" opacity="0.7"/>
  <ellipse cx="17" cy="24" rx="8" ry="2" fill="url(#mistGradient)" opacity="0.8"/>
  
  <!-- Central glow -->
  <circle cx="16" cy="16" r="6" fill="url(#mistGradient)" opacity="0.3"/>
  <circle cx="16" cy="16" r="3" fill="url(#mistGradient)" opacity="0.6"/>
</svg>
