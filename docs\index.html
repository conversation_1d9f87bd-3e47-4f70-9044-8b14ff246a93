<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mist - In-Memory MySQL Database</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/go.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/sql.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">            <div class="nav-brand">
                <img src="mist-icon.svg" alt="Mist" class="logo">
                <span class="brand-text">Mist</span>
            </div>                <div class="nav-links">
                <a href="#features">Features</a>
                <a href="#installation">Installation</a>
                <a href="#usage">Usage</a>
                <a href="#examples">Examples</a>
                <a href="https://github.com/abbychau/mist" class="github-link" target="_blank">
                    <span class="github-icon">⭐</span> GitHub
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Mist
                    <span class="gradient-text">In-Memory MySQL Database</span>
                </h1>
                <p class="hero-subtitle">
                    A lightweight, thread-safe SQL database engine with MySQL-compatible syntax.
                    Built for speed, simplicity, and seamless integration.
                </p>
                <div class="hero-buttons">
                    <a href="#installation" class="btn btn-primary">Get Started</a>
                    <a href="https://github.com/abbychau/mist" class="btn btn-secondary" target="_blank">
                        View on GitHub
                    </a>
                </div>
            </div>
            <div class="hero-demo">
                <div class="terminal">
                    <div class="terminal-header">
                        <div class="terminal-controls">
                            <span class="control red"></span>
                            <span class="control yellow"></span>
                            <span class="control green"></span>
                        </div>
                        <span class="terminal-title">mist-demo.go</span>
                    </div>
                    <div class="terminal-body">                        <pre><code class="language-go">engine := mist.NewSQLEngine()

// Create table with auto increment
engine.Execute(`CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50),
    age INT
)`)

// Insert data (ID auto-generated)
engine.Execute("INSERT INTO users (name, age) VALUES ('Alice', 30)")

// Query with aggregates
result, _ := engine.Execute("SELECT * FROM users")
mist.PrintResult(result)</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </section>    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">Core Features</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3><span class="feature-icon">🚀</span> Lightning Fast</h3>
                    <p>In-memory storage ensures blazing fast query execution with zero disk I/O overhead.</p>
                </div>                <div class="feature-card">
                    <h3><span class="feature-icon">🔗</span> MySQL Compatible</h3>
                    <p>Built with TiDB parser for full MySQL syntax compatibility. No learning curve required.</p>
                </div>
                <div class="feature-card">
                    <h3><span class="feature-icon">🔢</span> Auto Increment</h3>
                    <p>Automatic ID generation with AUTO_INCREMENT support for seamless primary key management.</p>
                </div>
                <div class="feature-card">
                    <h3><span class="feature-icon">🛡️</span> Thread Safe</h3>
                    <p>Concurrent operations are handled safely, making it perfect for multi-threaded applications.</p>
                </div>
                <div class="feature-card">
                    <h3><span class="feature-icon">📦</span> Zero Dependencies</h3>
                    <p>Self-contained Go library with no external database server requirements.</p>
                </div>
                <div class="feature-card">
                    <h3><span class="feature-icon">🔍</span> Advanced Queries</h3>
                    <p>Support for JOINs, subqueries, aggregates, indexes, and complex WHERE clauses.</p>
                </div>
                <div class="feature-card">
                    <h3><span class="feature-icon">📊</span> Query Recording</h3>
                    <p>Built-in query recording for debugging, auditing, and performance analysis with thread-safe operations.</p>
                </div>
                <div class="feature-card">
                    <h3><span class="feature-icon">🎯</span> Precise Tracking</h3>
                    <p>Record exact SQL queries as they're executed with immutable logs and zero performance overhead when disabled.</p>
                </div>
                <div class="feature-card">
                    <h3><span class="feature-icon">🔐</span> Secure & Isolated</h3>
                    <p>Recording sessions prevent external modification and ensure data integrity for audit trails.</p>
                </div>
                <div class="feature-card">
                    <h3><span class="feature-icon">🧪</span> Testing & Debug</h3>
                    <p>Perfect for unit testing, compliance logging, performance analysis, and migration tools.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Installation Section -->
    <section id="installation" class="installation">
        <div class="container">
            <h2 class="section-title">Quick Installation</h2>
            <div class="installation-options">
                <div class="install-option">
                    <h3>As a Go Library</h3>
                    <div class="code-block">
                        <pre><code class="language-bash">go get github.com/abbychau/mist</code></pre>
                        <button class="copy-btn" onclick="copyToClipboard('go get github.com/abbychau/mist')">📋</button>
                    </div>
                </div>
                <div class="install-option">
                    <h3>Standalone Application</h3>
                    <div class="code-block">
                        <pre><code class="language-bash">git clone https://github.com/abbychau/mist
cd mist
go mod tidy
go build .</code></pre>
                        <button class="copy-btn" onclick="copyToClipboard('git clone https://github.com/abbychau/mist\ncd mist\ngo mod tidy\ngo build .')">📋</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Usage Section -->
    <section id="usage" class="usage">
        <div class="container">
            <h2 class="section-title">Getting Started</h2>                <div class="usage-tabs">
                <div class="tab-buttons">
                    <button class="tab-btn active" onclick="showTab('library')">Library Usage</button>
                    <button class="tab-btn" onclick="showTab('recording-tab')">Query Recording</button>
                    <button class="tab-btn" onclick="showTab('interactive')">Interactive Mode</button>
                    <button class="tab-btn" onclick="showTab('advanced')">Advanced Features</button>
                </div>
                
                <div id="library" class="tab-content active">
                    <div class="code-example">
                        <pre><code class="language-go">package main

import (
    "fmt"
    "log"
    "github.com/abbychau/mist"
)

func main() {
    // Create a new SQL engine
    engine := mist.NewSQLEngine()

    // Create a table
    _, err := engine.Execute(`CREATE TABLE products (
        id INT PRIMARY KEY,
        name VARCHAR(100),
        price FLOAT,
        category VARCHAR(50)
    )`)
    if err != nil {
        log.Fatal(err)
    }

    // Insert data
    queries := []string{
        "INSERT INTO products VALUES (1, 'Laptop', 999.99, 'Electronics')",
        "INSERT INTO products VALUES (2, 'Book', 29.99, 'Education')",
        "INSERT INTO products VALUES (3, 'Headphones', 199.99, 'Electronics')",
    }
    
    for _, query := range queries {
        _, err := engine.Execute(query)
        if err != nil {
            log.Fatal(err)
        }
    }

    // Query with aggregates
    result, err := engine.Execute(`
        SELECT category, COUNT(*), AVG(price) 
        FROM products 
        WHERE price > 50 
        GROUP BY category
    `)
    if err != nil {
        log.Fatal(err)
    }

    // Print results
    mist.PrintResult(result)
}</code></pre>
                    </div>                </div>

                <div id="recording-tab" class="tab-content">
                    <div class="code-example">
                        <pre><code class="language-go">package main

import (
    "fmt"
    "github.com/abbychau/mist"
)

func main() {
    engine := mist.NewSQLEngine()
    
    // Setup initial data
    engine.Execute("CREATE TABLE orders (id INT, customer VARCHAR(50), amount FLOAT)")
    
    // Start recording all queries
    engine.StartRecording()
    
    // Execute business logic queries
    engine.Execute("INSERT INTO orders VALUES (1, 'Alice', 299.99)")
    engine.Execute("INSERT INTO orders VALUES (2, 'Bob', 149.50)")
    engine.Execute("UPDATE orders SET amount = 249.99 WHERE id = 1")
    engine.Execute("SELECT customer, SUM(amount) FROM orders GROUP BY customer")
    
    // Stop recording
    engine.EndRecording()
    
    // Analyze recorded queries
    queries := engine.GetRecordedQueries()
    fmt.Printf("Business logic executed %d queries:\n", len(queries))
    
    for i, query := range queries {
        fmt.Printf("%d. %s\n", i+1, query)
    }
    
    // Use for debugging, testing, or audit logs
    // Perfect for understanding query execution patterns
}</code></pre>
                    </div>
                </div>

                <div id="interactive" class="tab-content">
                    <div class="code-example">
                        <pre><code class="language-bash"># Start interactive mode
./mist -i

# Now you can run SQL commands directly:
mist> CREATE TABLE users (id INT, name VARCHAR(50), age INT);
Table 'users' created successfully.

mist> INSERT INTO users VALUES (1, 'Alice', 30), (2, 'Bob', 25);
2 rows inserted.

mist> SELECT * FROM users WHERE age > 25;
+----+-------+-----+
| id | name  | age |
+----+-------+-----+
| 1  | Alice | 30  |
+----+-------+-----+

mist> SELECT COUNT(*), AVG(age) FROM users;
+----------+----------+
| COUNT(*) | AVG(age) |
+----------+----------+
| 2        | 27.5     |
+----------+----------+</code></pre>
                    </div>
                </div>

                <div id="advanced" class="tab-content">
                    <div class="code-example">
                        <pre><code class="language-sql">-- Create indexes for better performance
CREATE INDEX idx_age ON users (age);
CREATE INDEX idx_category ON products (category);

-- Complex JOINs with aggregates
SELECT 
    d.name AS department,
    COUNT(u.id) AS employee_count,
    AVG(u.salary) AS avg_salary
FROM departments d
LEFT JOIN users u ON d.id = u.department_id
GROUP BY d.name
HAVING COUNT(u.id) > 2;

-- Subqueries in FROM clause
SELECT * FROM (
    SELECT name, age, 
           CASE WHEN age > 30 THEN 'Senior' ELSE 'Junior' END as level
    FROM users
) AS categorized
WHERE level = 'Senior';

-- ALTER TABLE operations
ALTER TABLE users ADD COLUMN email VARCHAR(100);
ALTER TABLE users MODIFY COLUMN name VARCHAR(200);
ALTER TABLE users DROP COLUMN temp_field;

-- Advanced WHERE clauses
SELECT * FROM products 
WHERE price BETWEEN 100 AND 500 
  AND category IN ('Electronics', 'Books')
  AND name LIKE '%Pro%'
LIMIT 10 OFFSET 5;</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </section>    <!-- Examples Section -->
    <section id="examples" class="examples">
        <div class="container">
            <h2 class="section-title">Supported Operations</h2>
            <div class="examples-grid">                <div class="example-card">
                    <h3>Schema & Structure</h3>
                    <ul>
                        <li>CREATE/ALTER/DROP TABLE</li>
                        <li>AUTO_INCREMENT primary keys</li>
                        <li>CREATE/DROP INDEX</li>
                        <li>Column constraints & modifications</li>
                        <li>SHOW TABLES/INDEX</li>
                    </ul>
                </div>
                <div class="example-card">
                    <h3>Data Operations</h3>
                    <ul>
                        <li>INSERT/UPDATE/DELETE/REPLACE</li>
                        <li>Complex WHERE conditions</li>
                        <li>Batch operations & transactions</li>
                        <li>Multiple value inserts</li>
                    </ul>
                </div>
                <div class="example-card">
                    <h3>Advanced Queries</h3>
                    <ul>
                        <li>JOINs (INNER/LEFT/RIGHT) & subqueries</li>
                        <li>Aggregates & GROUP BY/HAVING</li>
                        <li>ORDER BY, LIMIT, OFFSET</li>
                        <li>CASE expressions & functions</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">            <div class="footer-content">
                <div class="footer-brand">
                    <img src="mist-icon.svg" alt="Mist" class="logo">
                    <span class="brand-text">Mist</span>
                </div>
                <div class="footer-links">
                    <a href="https://github.com/abbychau/mist" target="_blank">GitHub</a>
                    <a href="https://github.com/abbychau/mist/issues" target="_blank">Issues</a>
                    <a href="https://github.com/abbychau/mist/blob/main/LICENSE" target="_blank">License</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Mist Database. Built with ❤️ in Go.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
