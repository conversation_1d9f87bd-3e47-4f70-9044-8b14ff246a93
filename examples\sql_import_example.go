package main

import (
	"fmt"
	"log"
	"strings"

	"github.com/abbychau/mist/mist"
)

func main() {
	// Create a new SQL engine
	engine := mist.NewSQLEngine()

	fmt.Println("=== SQL File Import Example ===")
	fmt.Println()

	// Example 1: Basic SQL file import
	fmt.Println("1. Importing SQL file with sample data...")
	results, err := engine.ImportSQLFile("examples/sample_data.sql")
	if err != nil {
		log.Fatalf("Failed to import SQL file: %v", err)
	}

	fmt.Printf("Successfully executed %d statements from SQL file\n", len(results))
	fmt.Println()

	// Example 2: Verify the data was imported correctly
	fmt.Println("2. Verifying imported data...")

	// Check departments
	result, err := engine.Execute("SELECT COUNT(*) FROM departments")
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("Departments table:")
	mist.PrintResult(result)

	// Check employees
	result, err = engine.Execute("SELECT COUNT(*) FROM employees")
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("Employees table:")
	mist.PrintResult(result)

	// Check projects
	result, err = engine.Execute("SELECT COUNT(*) FROM projects")
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("Projects table:")
	mist.PrintResult(result)
	fmt.Println()

	// Example 3: Query the imported data
	fmt.Println("3. Querying imported data...")

	// Show all departments
	result, err = engine.Execute("SELECT * FROM departments")
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("All departments:")
	mist.PrintResult(result)
	fmt.Println()

	// Show employees with their department names (JOIN)
	result, err = engine.Execute(`
		SELECT e.name, e.email, d.name as department, e.salary 
		FROM employees e 
		JOIN departments d ON e.department_id = d.id 
		ORDER BY e.salary DESC
	`)
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("Employees with departments (ordered by salary):")
	mist.PrintResult(result)
	fmt.Println()

	// Example 4: Import from string (simulating ImportSQLFileFromReader)
	fmt.Println("4. Importing SQL from string...")
	sqlContent := `
		-- Add a new department
		INSERT INTO departments VALUES (5, 'Research', 400000.0, 'Building D');
		
		-- Add a new employee
		INSERT INTO employees (name, email, department_id, salary, hire_date) 
		VALUES ('Helen Zhang', '<EMAIL>', 5, 95000.0, '2023-05-15');
	`

	results, err = engine.ImportSQLFileFromReader(strings.NewReader(sqlContent))
	if err != nil {
		log.Fatalf("Failed to import SQL from string: %v", err)
	}

	fmt.Printf("Successfully executed %d statements from string\n", len(results))

	// Verify the new data
	result, err = engine.Execute("SELECT * FROM departments WHERE id = 5")
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("New department:")
	mist.PrintResult(result)
	fmt.Println()

	// Example 5: Import with progress reporting
	fmt.Println("5. Importing SQL with progress reporting...")

	// Create another SQL content for demonstration
	progressSQLContent := `
		UPDATE employees SET salary = salary * 1.05 WHERE department_id = 1;
		UPDATE employees SET salary = salary * 1.03 WHERE department_id = 2;
		UPDATE employees SET salary = salary * 1.04 WHERE department_id = 3;
		SELECT AVG(salary) FROM employees;
	`

	// Import the progress SQL content using ImportSQLFileFromReader
	fmt.Println("Executing salary updates with progress:")
	results, err = engine.ImportSQLFileFromReader(strings.NewReader(progressSQLContent))
	if err != nil {
		fmt.Printf("Error executing salary updates: %v\n", err)
	} else {
		fmt.Printf("Successfully executed %d salary update statements\n", len(results))
	}
	fmt.Println()

	// Example 6: Show final statistics
	fmt.Println("6. Final database statistics...")

	queries := map[string]string{
		"Total Departments": "SELECT COUNT(*) FROM departments",
		"Total Employees":   "SELECT COUNT(*) FROM employees",
		"Total Projects":    "SELECT COUNT(*) FROM projects",
		"Average Salary":    "SELECT AVG(salary) FROM employees",
		"Highest Salary":    "SELECT MAX(salary) FROM employees",
	}

	for description, query := range queries {
		result, err := engine.Execute(query)
		if err != nil {
			fmt.Printf("Error executing %s: %v\n", description, err)
			continue
		}
		fmt.Printf("%s: ", description)
		mist.PrintResult(result)
	}

	fmt.Println("\n=== SQL Import Example Completed ===")
	fmt.Println("Features demonstrated:")
	fmt.Println("✓ ImportSQLFile() - Import from .sql file")
	fmt.Println("✓ ImportSQLFileFromReader() - Import from io.Reader")
	fmt.Println("✓ ImportSQLFileWithProgress() - Import with progress callback")
	fmt.Println("✓ Automatic handling of multiple SQL statements")
	fmt.Println("✓ Comment filtering (-- and # comments)")
	fmt.Println("✓ Error handling and reporting")
}
